using System.Text.Json;
using DuckDB.NET.Data;
using TimeLens.Client.Data.Context;
using TimeLens.Client.Models;

namespace TimeLens.Client.Data.Repositories
{
    /// <summary>
    /// 增强的ConceptTheme Repository实现
    /// </summary>
    public class EnhancedConceptThemeRepository(
        IEnhancedDuckDbContext context,
        ILogger<EnhancedConceptThemeRepository>? logger = null)
        : EnhancedBaseRepository<ConceptTheme>(context, "concept_themes", logger), IConceptThemeRepository
    {
        protected override string GenerateCreateTableSql()
        {
            return @"
                CREATE TABLE IF NOT EXISTS concept_themes (
                    id VARCHAR PRIMARY KEY NOT NULL,
                    name VARCHAR NOT NULL UNIQUE,
                    heat INTEGER NOT NULL DEFAULT 0,
                    active_stocks VARCHAR[],
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )";
        }

        protected override ConceptTheme MapFromReader(DuckDBDataReader reader)
        {
            var activeStocks = new List<string>();

            try
            {
                activeStocks = reader.IsDBNull(3) ? [] : reader.GetFieldValue<List<string>>(3);
            }
            catch (JsonException ex)
            {
                Logger?.LogWarning(ex, "Failed to deserialize active stocks for concept theme");
            }

            return new ConceptTheme
            {
                Id = reader.GetString(0),
                Name = reader.GetString(1),
                Heat = reader.GetInt32(2),
                ActiveStocks = activeStocks,
                CreatedAt = reader.GetDateTime(4),
                UpdatedAt = reader.GetDateTime(5)
            };
        }

        protected override DuckDBParameter[] GetInsertParameters(ConceptTheme entity)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.UpdatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.Name),
                new DuckDBParameter(entity.Heat),
                new DuckDBParameter(entity.ActiveStocks),
                new DuckDBParameter(entity.CreatedAt),
                new DuckDBParameter(entity.UpdatedAt)
            ];
        }

        protected override DuckDBParameter[] GetUpdateParameters(ConceptTheme entity)
        {
            entity.UpdatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.Name),
                new DuckDBParameter(entity.Heat),
                new DuckDBParameter(entity.ActiveStocks),
                new DuckDBParameter(entity.UpdatedAt)
            ];
        }

        public override async Task<ConceptTheme?> GetByIdAsync(object id)
        {
            var sql = "SELECT * FROM concept_themes WHERE id = $1";
            var results = await QueryInternalAsync(sql, new DuckDBParameter(id));
            return results.FirstOrDefault();
        }

        public override async Task<IEnumerable<ConceptTheme>> GetAllAsync()
        {
            var sql = "SELECT * FROM concept_themes ORDER BY heat DESC, name ASC";
            return await QueryInternalAsync(sql);
        }

        public override async Task<ConceptTheme> AddAsync(ConceptTheme entity)
        {
            await EnsureTableExistsAsync();

            var sql = @"INSERT INTO concept_themes
                       (id, name, heat, active_stocks, created_at, updated_at)
                       VALUES ($1, $2, $3, $4, $5, $6)";

            var parameters = GetInsertParameters(entity);
            await Context.ExecuteNonQueryAsync(sql, parameters);

            Logger?.LogInformation("ConceptTheme {Id} ({Name}) added successfully", entity.Id, entity.Name);
            return entity;
        }

        public override async Task<ConceptTheme> UpdateAsync(ConceptTheme entity)
        {
            await EnsureTableExistsAsync();

            var sql = @"UPDATE concept_themes
                       SET name = $2, heat = $3, active_stocks = $4, updated_at = $5
                       WHERE id = $1";

            var parameters = GetUpdateParameters(entity);
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, parameters);

            if (rowsAffected == 0)
            {
                throw new InvalidOperationException($"ConceptTheme with ID {entity.Id} not found");
            }

            Logger?.LogInformation("ConceptTheme {Id} ({Name}) updated successfully", entity.Id, entity.Name);
            return entity;
        }

        public override async Task<bool> DeleteAsync(object id)
        {
            await EnsureTableExistsAsync();

            var sql = "DELETE FROM concept_themes WHERE id = $1";
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(id));

            var deleted = rowsAffected > 0;
            if (deleted)
            {
                Logger?.LogInformation("ConceptTheme {Id} deleted successfully", id);
            }

            return deleted;
        }

        // ConceptTheme特定的查询方法
        public async Task<IEnumerable<ConceptTheme>> GetByNameAsync(string name)
        {
            var sql = "SELECT * FROM concept_themes WHERE name = $1";
            return await QueryInternalAsync(sql, new DuckDBParameter(name));
        }

        public async Task<IEnumerable<ConceptTheme>> GetByHeatRangeAsync(int minHeat, int maxHeat)
        {
            var sql = "SELECT * FROM concept_themes WHERE heat BETWEEN $1 AND $2 ORDER BY heat DESC";
            return await QueryInternalAsync(sql, 
                new DuckDBParameter(minHeat),
                new DuckDBParameter(maxHeat));
        }

        public async Task<IEnumerable<ConceptTheme>> GetByActiveStockAsync(string stockCode)
        {
            var sql = "SELECT * FROM concept_themes WHERE active_stocks LIKE $1 ORDER BY heat DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter( $"%{stockCode}%"));
        }

        public async Task<IEnumerable<ConceptTheme>> SearchByNameAsync(string searchTerm)
        {
            var sql = "SELECT * FROM concept_themes WHERE name LIKE $1 ORDER BY heat DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter($"%{searchTerm}%"));
        }

        // 热度相关查询
        public async Task<IEnumerable<ConceptTheme>> GetHottestThemesAsync(int count = 10)
        {
            var sql = $"SELECT * FROM concept_themes ORDER BY heat DESC LIMIT {count}";
            return await QueryInternalAsync(sql);
        }

        public async Task<IEnumerable<ConceptTheme>> GetCoolestThemesAsync(int count = 10)
        {
            var sql = $"SELECT * FROM concept_themes ORDER BY heat ASC LIMIT {count}";
            return await QueryInternalAsync(sql);
        }

        public async Task<ConceptTheme?> GetHottestThemeAsync()
        {
            var sql = "SELECT * FROM concept_themes ORDER BY heat DESC LIMIT 1";
            var results = await QueryInternalAsync(sql);
            return results.FirstOrDefault();
        }

        public async Task<double> GetAverageHeatAsync()
        {
            await EnsureTableExistsAsync();
            var sql = "SELECT AVG(heat) FROM concept_themes";
            return await Context.ExecuteScalarAsync<double>(sql);
        }

        // 股票相关查询
        public async Task<IEnumerable<ConceptTheme>> GetThemesByStockAsync(string stockCode)
        {
            return await GetByActiveStockAsync(stockCode);
        }

        public async Task<Dictionary<string, int>> GetStockThemeCountAsync()
        {
            // 这是一个简化实现，实际项目中可能需要更复杂的JSON查询
            var allThemes = await GetAllAsync();
            var stockCounts = new Dictionary<string, int>();
            
            foreach (var theme in allThemes)
            {
                foreach (var stock in theme.ActiveStocks)
                {
                    stockCounts[stock] = stockCounts.GetValueOrDefault(stock, 0) + 1;
                }
            }
            
            return stockCounts.OrderByDescending(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
        }

        public async Task<IEnumerable<string>> GetAllActiveStocksAsync()
        {
            var allThemes = await GetAllAsync();
            var allStocks = new HashSet<string>();
            
            foreach (var theme in allThemes)
            {
                foreach (var stock in theme.ActiveStocks)
                {
                    allStocks.Add(stock);
                }
            }
            
            return allStocks.OrderBy(x => x);
        }

        // 统计方法
        public async Task<int> GetTotalThemeCountAsync()
        {
            await EnsureTableExistsAsync();
            var sql = "SELECT COUNT(*) FROM concept_themes";
            var count = await Context.ExecuteScalarAsync<long>(sql);
            return (int)count;
        }

        public async Task<Dictionary<int, int>> GetHeatDistributionAsync()
        {
            await EnsureTableExistsAsync();

            var sql = @"SELECT
                           CASE
                               WHEN heat < 20 THEN 0
                               WHEN heat < 40 THEN 20
                               WHEN heat < 60 THEN 40
                               WHEN heat < 80 THEN 60
                               ELSE 80
                           END as heat_range,
                           COUNT(*) as count
                       FROM concept_themes
                       GROUP BY heat_range
                       ORDER BY heat_range";

            var results = new Dictionary<int, int>();
            await using var reader = await Context.ExecuteReaderAsync(sql);
            while (await reader.ReadAsync())
            {
                results[reader.GetInt32(0)] = reader.GetInt32(1);
            }
            
            return results;
        }

        public async Task<IEnumerable<ConceptTheme>> GetThemesCreatedAfterAsync(DateTime date)
        {
            var sql = "SELECT * FROM concept_themes WHERE created_at > $1 ORDER BY created_at DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter( date));
        }

        public async Task<IEnumerable<ConceptTheme>> GetThemesUpdatedAfterAsync(DateTime date)
        {
            var sql = "SELECT * FROM concept_themes WHERE updated_at > $1 ORDER BY updated_at DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter(date));
        }
    }
}
