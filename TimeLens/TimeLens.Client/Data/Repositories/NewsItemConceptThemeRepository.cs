using DuckDB.NET.Data;
using TimeLens.Client.Data.Context;
using TimeLens.Client.Models;

namespace TimeLens.Client.Data.Repositories
{
    /// <summary>
    /// NewsItem 和 ConceptTheme 关联表的 Repository 实现
    /// </summary>
    public class NewsItemConceptThemeRepository(
        IEnhancedDuckDbContext context,
        ILogger<NewsItemConceptThemeRepository>? logger = null)
        : EnhancedBaseRepository<NewsItemConceptTheme>(context, "news_item_concept_themes", logger),
            INewsItemConceptThemeRepository
    {
        protected override string GenerateCreateTableSql()
        {
            return @"
                CREATE TABLE IF NOT EXISTS news_item_concept_themes (
                    id VARCHAR PRIMARY KEY NOT NULL,
                    news_item_id VARCHAR NOT NULL,
                    concept_theme_id VARCHAR NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(news_item_id, concept_theme_id)
                )";
        }

        protected override NewsItemConceptTheme MapFromReader(DuckDBDataReader reader)
        {
            return new NewsItemConceptTheme
            {
                Id = reader.GetString(0),
                NewsItemId = reader.GetString(1),
                ConceptThemeId = reader.GetString(2),
                CreatedAt = reader.GetDateTime(3)
            };
        }

        protected override DuckDBParameter[] GetInsertParameters(NewsItemConceptTheme entity)
        {
            entity.CreatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.NewsItemId),
                new DuckDBParameter(entity.ConceptThemeId),
                new DuckDBParameter(entity.CreatedAt)
            ];
        }

        protected override DuckDBParameter[] GetUpdateParameters(NewsItemConceptTheme entity)
        {
            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.NewsItemId),
                new DuckDBParameter(entity.ConceptThemeId),
                new DuckDBParameter(entity.CreatedAt)
            ];
        }

        public override async Task<NewsItemConceptTheme?> GetByIdAsync(object id)
        {
            const string sql = "SELECT * FROM news_item_concept_themes WHERE id = $1";
            var results = await QueryInternalAsync(sql, new DuckDBParameter(id));
            return results.FirstOrDefault();
        }

        public override async Task<IEnumerable<NewsItemConceptTheme>> GetAllAsync()
        {
            const string sql = "SELECT * FROM news_item_concept_themes ORDER BY created_at DESC";
            return await QueryInternalAsync(sql);
        }

        public override async Task<NewsItemConceptTheme> AddAsync(NewsItemConceptTheme entity)
        {
            const string sql = @"INSERT INTO news_item_concept_themes 
                       (id, news_item_id, concept_theme_id, created_at) 
                       VALUES ($1, $2, $3, $4)";
            
            var parameters = GetInsertParameters(entity);
            await Context.ExecuteNonQueryAsync(sql, parameters);
            
            Logger?.LogInformation("NewsItemConceptTheme {Id} added successfully", entity.Id);
            return entity;
        }

        public override async Task<NewsItemConceptTheme> UpdateAsync(NewsItemConceptTheme entity)
        {
            const string sql = @"UPDATE news_item_concept_themes 
                       SET news_item_id = $2, concept_theme_id = $3, created_at = $4 
                       WHERE id = $1";
            
            var parameters = GetUpdateParameters(entity);
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, parameters);
            
            if (rowsAffected == 0)
            {
                throw new InvalidOperationException($"NewsItemConceptTheme with ID {entity.Id} not found");
            }
            
            Logger?.LogInformation("NewsItemConceptTheme {Id} updated successfully", entity.Id);
            return entity;
        }

        public override async Task<bool> DeleteAsync(object id)
        {
            const string sql = "DELETE FROM news_item_concept_themes WHERE id = $1";
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(id));
            
            var deleted = rowsAffected > 0;
            if (deleted)
            {
                Logger?.LogInformation("NewsItemConceptTheme {Id} deleted successfully", id);
            }
            
            return deleted;
        }

        // 实现接口方法
        public async Task<IEnumerable<ConceptTheme>> GetConceptThemesByNewsItemIdAsync(string newsItemId)
        {
            const string sql = @"
                SELECT ct.id, ct.name, ct.heat, ct.active_stocks, ct.created_at, ct.updated_at
                FROM concept_themes ct
                INNER JOIN news_item_concept_themes nict ON ct.id = nict.concept_theme_id
                WHERE nict.news_item_id = $1
                ORDER BY ct.heat DESC";

            return await Context.QueryAsync<ConceptTheme>(sql, reader =>
            {
                var activeStocks = reader.IsDBNull(3) ? null : reader.GetFieldValue<List<string>>(3);

                return new ConceptTheme
                {
                    Id = reader.GetString(0),
                    Name = reader.GetString(1),
                    Heat = reader.GetInt32(2),
                    ActiveStocks = activeStocks,
                    CreatedAt = reader.GetDateTime(4),
                    UpdatedAt = reader.GetDateTime(5)
                };
            }, new DuckDBParameter(newsItemId));
        }

        public async Task<IEnumerable<NewsItem>> GetNewsItemsByConceptThemeIdAsync(string conceptThemeId)
        {
            const string sql = @"
                SELECT ni.id, ni.title, ni.content, ni.publish_date, ni.company, ni.tags, ni.category, ni.summary, ni.source, ni.importance, ni.created_at, ni.updated_at
                FROM news_items ni
                INNER JOIN news_item_concept_themes nict ON ni.id = nict.news_item_id
                WHERE nict.concept_theme_id = $1
                ORDER BY ni.publish_date DESC";

            return await Context.QueryAsync<NewsItem>(sql, reader =>
            {
                var tags = reader.IsDBNull(5) ? null : reader.GetFieldValue<List<string>>(5);

                return new NewsItem
                {
                    Id = reader.GetString(0),
                    Title = reader.GetString(1),
                    Content = reader.GetString(2),
                    PublishDate = reader.GetDateTime(3),
                    Company = reader.GetString(4),
                    Tags = tags,
                    Category = (NewsCategory)reader.GetInt32(6),
                    Summary = reader.GetString(7),
                    Source = reader.IsDBNull(8) ? null : reader.GetString(8),
                    Importance = (ImportanceCategory)reader.GetInt32(9),
                    CreatedAt = reader.GetDateTime(10),
                    UpdatedAt = reader.GetDateTime(11)
                };
            }, new DuckDBParameter(conceptThemeId));
        }

        public async Task<Dictionary<string, List<ConceptTheme>>> GetConceptThemesByNewsItemIdsAsync(IEnumerable<string> newsItemIds)
        {
            var ids = newsItemIds.ToArray();
            if (ids.Length == 0) return new Dictionary<string, List<ConceptTheme>>();

            var placeholders = string.Join(", ", ids.Select((_, i) => $"${i + 1}"));
            var sql = $@"
                SELECT nict.news_item_id, ct.id, ct.name, ct.heat, ct.active_stocks, ct.created_at, ct.updated_at
                FROM concept_themes ct
                INNER JOIN news_item_concept_themes nict ON ct.id = nict.concept_theme_id
                WHERE nict.news_item_id IN ({placeholders})
                ORDER BY nict.news_item_id, ct.heat DESC";

            var parameters = ids.Select((id, i) => new DuckDBParameter($"${i + 1}", id)).ToArray();
            var result = new Dictionary<string, List<ConceptTheme>>();

            await using var reader = await Context.ExecuteReaderAsync(sql, parameters);
            while (await reader.ReadAsync())
            {
                var newsItemId = reader.GetString(0);
                var activeStocks = reader.IsDBNull(4) ? null : reader.GetFieldValue<List<string>>(4);

                var conceptTheme = new ConceptTheme
                {
                    Id = reader.GetString(1),
                    Name = reader.GetString(2),
                    Heat = reader.GetInt32(3),
                    ActiveStocks = activeStocks,
                    CreatedAt = reader.GetDateTime(5),
                    UpdatedAt = reader.GetDateTime(6)
                };

                if (!result.ContainsKey(newsItemId))
                {
                    result[newsItemId] = new List<ConceptTheme>();
                }
                result[newsItemId].Add(conceptTheme);
            }

            return result;
        }

        public async Task<Dictionary<string, List<NewsItem>>> GetNewsItemsByConceptThemeIdsAsync(IEnumerable<string> conceptThemeIds)
        {
            var ids = conceptThemeIds.ToArray();
            if (ids.Length == 0) return new Dictionary<string, List<NewsItem>>();

            var placeholders = string.Join(", ", ids.Select((_, i) => $"${i + 1}"));
            var sql = $@"
                SELECT nict.concept_theme_id, ni.id, ni.title, ni.content, ni.publish_date, ni.company, ni.tags, ni.category, ni.summary, ni.source, ni.importance, ni.created_at, ni.updated_at
                FROM news_items ni
                INNER JOIN news_item_concept_themes nict ON ni.id = nict.news_item_id
                WHERE nict.concept_theme_id IN ({placeholders})
                ORDER BY nict.concept_theme_id, ni.publish_date DESC";

            var parameters = ids.Select((id, i) => new DuckDBParameter($"${i + 1}", id)).ToArray();
            var result = new Dictionary<string, List<NewsItem>>();

            await using var reader = await Context.ExecuteReaderAsync(sql, parameters);
            while (await reader.ReadAsync())
            {
                var conceptThemeId = reader.GetString(0);
                var tags = reader.IsDBNull(6) ? null : reader.GetFieldValue<List<string>>(6);

                var newsItem = new NewsItem
                {
                    Id = reader.GetString(1),
                    Title = reader.GetString(2),
                    Content = reader.GetString(3),
                    PublishDate = reader.GetDateTime(4),
                    Company = reader.GetString(5),
                    Tags = tags,
                    Category = (NewsCategory)reader.GetInt32(7),
                    Summary = reader.GetString(8),
                    Source = reader.IsDBNull(9) ? null : reader.GetString(9),
                    Importance = (ImportanceCategory)reader.GetInt32(10),
                    CreatedAt = reader.GetDateTime(11),
                    UpdatedAt = reader.GetDateTime(12)
                };

                if (!result.TryGetValue(conceptThemeId, out var value))
                {
                    value = ([]);
                    result[conceptThemeId] = value;
                }

                value.Add(newsItem);
            }

            return result;
        }

        public async Task AddNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId)
        {
            var entity = new NewsItemConceptTheme
            {
                NewsItemId = newsItemId,
                ConceptThemeId = conceptThemeId
            };
            await AddAsync(entity);
        }

        public async Task AddNewsItemConceptThemesAsync(string newsItemId, IEnumerable<string> conceptThemeIds)
        {
            var entities = conceptThemeIds.Select(id => new NewsItemConceptTheme
            {
                NewsItemId = newsItemId,
                ConceptThemeId = id
            });

            await AddRangeAsync(entities);
        }

        public async Task RemoveNewsItemConceptThemeAsync(string newsItemId, string conceptThemeId)
        {
            const string sql = "DELETE FROM news_item_concept_themes WHERE news_item_id = $1 AND concept_theme_id = $2";
            await Context.ExecuteNonQueryAsync(sql, 
                new DuckDBParameter(newsItemId),
                new DuckDBParameter(conceptThemeId));
        }

        public async Task RemoveAllConceptThemesFromNewsItemAsync(string newsItemId)
        {
            const string sql = "DELETE FROM news_item_concept_themes WHERE news_item_id = $1";
            await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(newsItemId));
        }

        public async Task RemoveAllNewsItemsFromConceptThemeAsync(string conceptThemeId)
        {
            const string sql = "DELETE FROM news_item_concept_themes WHERE concept_theme_id = $1";
            await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(conceptThemeId));
        }

        public async Task<bool> IsNewsItemConceptThemeLinkedAsync(string newsItemId, string conceptThemeId)
        {
            const string sql = "SELECT COUNT(*) FROM news_item_concept_themes WHERE news_item_id = $1 AND concept_theme_id = $2";
            var count = await Context.ExecuteScalarAsync<long>(sql, 
                new DuckDBParameter(newsItemId),
                new DuckDBParameter(conceptThemeId));
            return count > 0;
        }

        public async Task<int> GetNewsItemCountByConceptThemeIdAsync(string conceptThemeId)
        {
            const string sql = "SELECT COUNT(*) FROM news_item_concept_themes WHERE concept_theme_id = $1";
            var count = await Context.ExecuteScalarAsync<long>(sql, new DuckDBParameter(conceptThemeId));
            return (int)count;
        }

        public async Task<int> GetConceptThemeCountByNewsItemIdAsync(string newsItemId)
        {
            const string sql = "SELECT COUNT(*) FROM news_item_concept_themes WHERE news_item_id = $1";
            var count = await Context.ExecuteScalarAsync<long>(sql, new DuckDBParameter(newsItemId));
            return (int)count;
        }
        
        public async Task<IEnumerable<(ConceptTheme Theme, int NewsCount)>> GetMostPopularConceptThemesAsync(int topCount = 10)
        {
            var sql = $@"
                SELECT ct.id, ct.name, ct.heat, ct.active_stocks, ct.created_at, ct.updated_at, COUNT(nict.news_item_id) as news_count
                FROM concept_themes ct
                LEFT JOIN news_item_concept_themes nict ON ct.id = nict.concept_theme_id
                GROUP BY ct.id, ct.name, ct.heat, ct.active_stocks, ct.created_at, ct.updated_at
                ORDER BY news_count DESC, ct.heat DESC
                LIMIT {topCount}";

            var results = new List<(ConceptTheme, int)>();
            await using var reader = await Context.ExecuteReaderAsync(sql);
            while (await reader.ReadAsync())
            {
                var activeStocks = reader.IsDBNull(3) ? null : reader.GetFieldValue<List<string>>(3);

                var conceptTheme = new ConceptTheme
                {
                    Id = reader.GetString(0),
                    Name = reader.GetString(1),
                    Heat = reader.GetInt32(2),
                    ActiveStocks = activeStocks,
                    CreatedAt = reader.GetDateTime(4),
                    UpdatedAt = reader.GetDateTime(5)
                };

                var newsCount = reader.GetInt32(6);
                results.Add((conceptTheme, newsCount));
            }

            return results;
        }

        public async Task<Dictionary<string, int>> GetConceptThemeNewsCountStatisticsAsync()
        {
            const string sql = @"
                SELECT ct.name, COUNT(nict.news_item_id) as news_count
                FROM concept_themes ct
                LEFT JOIN news_item_concept_themes nict ON ct.id = nict.concept_theme_id
                GROUP BY ct.name
                ORDER BY news_count DESC";

            var results = new Dictionary<string, int>();
            await using var reader = await Context.ExecuteReaderAsync(sql);
            while (await reader.ReadAsync())
            {
                results[reader.GetString(0)] = reader.GetInt32(1);
            }

            return results;
        }
    }
}
