using DuckDB.NET.Data;
using TimeLens.Client.Data.Context;
using TimeLens.Client.Models;

namespace TimeLens.Client.Data.Repositories
{
    /// <summary>
    /// 增强的NewsItem Repository实现
    /// </summary>
    public class EnhancedNewsItemRepository(
        IEnhancedDuckDbContext context,
        INewsItemConceptThemeRepository relationRepository,
        ILogger<EnhancedNewsItemRepository>? logger = null)
        : EnhancedBaseRepository<NewsItem>(context, "news_items", logger), INewsItemRepository
    {
        protected override string GenerateCreateTableSql()
        {
            return @"
                CREATE TABLE IF NOT EXISTS news_items (
                    id VARCHAR PRIMARY KEY NOT NULL,
                    title VARCHAR NOT NULL,
                    content TEXT NOT NULL,
                    publish_date TIMESTAMP NOT NULL,
                    company VARCHAR NOT NULL,
                    tags VARCHAR[],
                    category INTEGER NOT NULL,
                    summary TEXT NOT NULL,
                    source VARCHAR,
                    importance INTEGER NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                )";
        }

        protected override NewsItem MapFromReader(DuckDBDataReader reader)
        {
            var tags = reader.GetFieldValue<List<string>>(5);

            return new NewsItem
            {
                Id = reader.GetString(0),
                Title = reader.GetString(1),
                Content = reader.GetString(2),
                PublishDate = reader.GetDateTime(3),
                Company = reader.GetString(4),
                Tags = tags,
                Category = (NewsCategory)reader.GetInt32(6),
                Summary = reader.GetString(7),
                Source = reader.IsDBNull(8) ? null : reader.GetString(8),
                Importance = (ImportanceCategory)reader.GetInt32(9),
                ConceptThemes = [], // 将通过关联查询填充
                CreatedAt = reader.GetDateTime(10),
                UpdatedAt = reader.GetDateTime(11)
            };
        }

        protected override DuckDBParameter[] GetInsertParameters(NewsItem entity)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.UpdatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.Title),
                new DuckDBParameter(entity.Content),
                new DuckDBParameter(entity.PublishDate),
                new DuckDBParameter(entity.Company),
                new DuckDBParameter(entity.Tags),
                new DuckDBParameter((int)entity.Category),
                new DuckDBParameter(entity.Summary),
                new DuckDBParameter(entity.Source),
                new DuckDBParameter((int)entity.Importance),
                new DuckDBParameter(entity.CreatedAt),
                new DuckDBParameter(entity.UpdatedAt)
            ];
        }

        protected override DuckDBParameter[] GetUpdateParameters(NewsItem entity)
        {
            entity.UpdatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.Title),
                new DuckDBParameter(entity.Content),
                new DuckDBParameter(entity.PublishDate),
                new DuckDBParameter(entity.Company),
                new DuckDBParameter(entity.Tags),
                new DuckDBParameter((int)entity.Category),
                new DuckDBParameter(entity.Summary),
                new DuckDBParameter(entity.Source),
                new DuckDBParameter((int)entity.Importance),
                new DuckDBParameter(entity.UpdatedAt)
            ];
        }

        public override async Task<NewsItem?> GetByIdAsync(object id)
        {
            const string sql = "SELECT * FROM news_items WHERE id = $1";
            var results = await QueryInternalAsync(sql, new DuckDBParameter(id));
            var newsItem = results.FirstOrDefault();

            if (newsItem != null)
            {
                // 加载关联的概念主题
                var conceptThemes = await relationRepository.GetConceptThemesByNewsItemIdAsync(newsItem.Id);
                newsItem.ConceptThemes.Clear();
                newsItem.ConceptThemes.AddRange(conceptThemes);
            }

            return newsItem;
        }

        /// <summary>
        /// 批量加载新闻项目及其概念主题
        /// </summary>
        public async Task<IEnumerable<NewsItem>> GetAllWithConceptThemesAsync()
        {
            var newsItems = await GetAllAsync();
            var newsItemsList = newsItems.ToList();

            if (newsItemsList.Count != 0)
            {
                var newsItemIds = newsItemsList.Select(ni => ni.Id).ToArray();
                var conceptThemesMap = await relationRepository.GetConceptThemesByNewsItemIdsAsync(newsItemIds);

                foreach (var newsItem in newsItemsList)
                {
                    if (conceptThemesMap.TryGetValue(newsItem.Id, out var themes))
                    {
                        newsItem.ConceptThemes.Clear();
                        newsItem.ConceptThemes.AddRange(themes);
                    }
                }
            }

            return newsItemsList;
        }

        public override async Task<IEnumerable<NewsItem>> GetAllAsync()
        {
            const string sql = "SELECT * FROM news_items ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql);
        }

        public override async Task<NewsItem> AddAsync(NewsItem entity)
        {
            await Context.BeginTransactionAsync();
            try
            {
                // 1. 插入新闻项目
                const string sql = @"INSERT INTO news_items
                           (id, title, content, publish_date, company, tags, category, summary, source, importance, created_at, updated_at)
                           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)";

                var parameters = GetInsertParameters(entity);
                await Context.ExecuteNonQueryAsync(sql, parameters);

                // 2. 添加概念主题关联
                if (entity.ConceptThemes.Count != 0)
                {
                    var conceptThemeIds = entity.ConceptThemes.Select(ct => ct.Id).ToArray();
                    await relationRepository.AddNewsItemConceptThemesAsync(entity.Id, conceptThemeIds);
                }

                await Context.CommitTransactionAsync();
                Logger?.LogInformation("NewsItem {Id} added successfully with {ThemeCount} concept themes",
                    entity.Id, entity.ConceptThemes.Count);
                return entity;
            }
            catch
            {
                await Context.RollbackTransactionAsync();
                throw;
            }
        }

        public override async Task<NewsItem> UpdateAsync(NewsItem entity)
        {
            const string sql = @"UPDATE news_items
                       SET title = $2, content = $3, publish_date = $4, company = $5,
                           tags = $6, category = $7, summary = $8, source = $9, importance = $10,
                           updated_at = $11
                       WHERE id = $1";
            
            var parameters = GetUpdateParameters(entity);
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, parameters);
            
            if (rowsAffected == 0)
            {
                throw new InvalidOperationException($"NewsItem with ID {entity.Id} not found");
            }
            
            Logger?.LogInformation("NewsItem {Id} updated successfully", entity.Id);
            return entity;
        }

        public override async Task<bool> DeleteAsync(object id)
        {
            const string sql = "DELETE FROM news_items WHERE id = $1";
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(id));
            
            var deleted = rowsAffected > 0;
            if (deleted)
            {
                Logger?.LogInformation("NewsItem {Id} deleted successfully", id);
            }
            
            return deleted;
        }

        // NewsItem特定的查询方法
        public async Task<IEnumerable<NewsItem>> GetByCompanyAsync(string company)
        {
            const string sql = "SELECT * FROM news_items WHERE company = $1 ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter(company));
        }

        public async Task<IEnumerable<NewsItem>> GetByCategoryAsync(NewsCategory category)
        {
            const string sql = "SELECT * FROM news_items WHERE category = $1 ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter((int)category));
        }

        public async Task<IEnumerable<NewsItem>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = "SELECT * FROM news_items WHERE publish_date BETWEEN $1 AND $2 ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql, 
                new DuckDBParameter(startDate),
                new DuckDBParameter(endDate));
        }

        public async Task<IEnumerable<NewsItem>> SearchByTitleAsync(string searchTerm)
        {
            const string sql = "SELECT * FROM news_items WHERE title LIKE $1 ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter($"%{searchTerm}%"));
        }

        public async Task<IEnumerable<NewsItem>> SearchByContentAsync(string searchTerm)
        {
            const string sql = "SELECT * FROM news_items WHERE content LIKE $1 ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter($"%{searchTerm}%"));
        }

        public async Task<IEnumerable<NewsItem>> GetByTagsAsync(params string[] tags)
        {
            if (tags.Length == 0) return [];
            
            var conditions = string.Join(" OR ", tags.Select((_, i) => $"tags LIKE ${i + 1}"));
            var sql = $"SELECT * FROM news_items WHERE {conditions} ORDER BY publish_date DESC";
            var parameters = tags.Select((tag, i) => new DuckDBParameter($"%{tag}%")).ToArray();
            
            return await QueryInternalAsync(sql, parameters);
        }

        public async Task<IEnumerable<NewsItem>> GetByConceptThemeAsync(string conceptThemeName)
        {
            const string sql = "SELECT * FROM news_items WHERE concept_themes LIKE $1 ORDER BY publish_date DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter($"%{conceptThemeName}%"));
        }

        // 统计方法
        public async Task<int> GetCountByCompanyAsync(string company)
        {
            const string sql = "SELECT COUNT(*) FROM news_items WHERE company = $1";
            var count = await Context.ExecuteScalarAsync<long>(sql, new DuckDBParameter(company));
            return (int)count;
        }

        public async Task<int> GetCountByCategoryAsync(NewsCategory category)
        {
            const string sql = "SELECT COUNT(*) FROM news_items WHERE category = $1";
            var count = await Context.ExecuteScalarAsync<long>(sql, new DuckDBParameter((int)category));
            return (int)count;
        }

        public async Task<Dictionary<string, int>> GetCompanyStatisticsAsync()
        {
            const string sql = "SELECT company, COUNT(*) as count FROM news_items GROUP BY company ORDER BY count DESC";
            var results = new Dictionary<string, int>();

            await using var reader = await Context.ExecuteReaderAsync(sql);
            while (await reader.ReadAsync())
            {
                results[reader.GetString(0)] = reader.GetInt32(1);
            }
            
            return results;
        }

        public async Task<Dictionary<NewsCategory, int>> GetCategoryStatisticsAsync()
        {
            const string sql = "SELECT category, COUNT(*) as count FROM news_items GROUP BY category";
            var results = new Dictionary<NewsCategory, int>();

            await using var reader = await Context.ExecuteReaderAsync(sql);
            while (await reader.ReadAsync())
            {
                var category = (NewsCategory)reader.GetInt32(0);
                results[category] = reader.GetInt32(1);
            }
            
            return results;
        }

        public async Task<Dictionary<string, int>> GetTagStatisticsAsync()
        {
            // 这是一个简化实现，实际项目中可能需要更复杂的JSON查询
            var allNews = await GetAllAsync();
            var tagCounts = new Dictionary<string, int>();
            
            foreach (var news in allNews)
            {
                foreach (var tag in news.Tags)
                {
                    tagCounts[tag] = tagCounts.GetValueOrDefault(tag, 0) + 1;
                }
            }
            
            return tagCounts.OrderByDescending(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
        }

        // 高级查询
        public async Task<IEnumerable<NewsItem>> GetRecentNewsAsync(int count = 10)
        {
            var sql = $"SELECT * FROM news_items ORDER BY publish_date DESC LIMIT {count}";
            return await QueryInternalAsync(sql);
        }

        public async Task<IEnumerable<NewsItem>> GetTopNewsByCompanyAsync(string company, int count = 10)
        {
            var sql = $"SELECT * FROM news_items WHERE company = $1 ORDER BY publish_date DESC LIMIT {count}";
            return await QueryInternalAsync(sql, new DuckDBParameter(company));
        }

        public async Task<IEnumerable<NewsItem>> GetRelatedNewsAsync(string newsItemId, int count = 5)
        {
            // 简化实现：基于相同公司和概念主题查找相关新闻
            var originalNews = await GetByIdAsync(newsItemId);
            if (originalNews == null) return [];
            
            var sql = $@"SELECT * FROM news_items 
                        WHERE id != $1 AND (company = $2 OR concept_themes LIKE $3) 
                        ORDER BY publish_date DESC LIMIT {count}";
            
            return await QueryInternalAsync(sql,
                new DuckDBParameter(newsItemId),
                new DuckDBParameter(originalNews.Company),
                new DuckDBParameter($"%{originalNews.ConceptThemes.FirstOrDefault()?.Name ?? ""}%"));
        }
    }
}
