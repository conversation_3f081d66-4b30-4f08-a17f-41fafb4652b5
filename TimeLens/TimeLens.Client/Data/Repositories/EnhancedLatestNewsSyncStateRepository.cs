using DuckDB.NET.Data;
using TimeLens.Client.Data.Context;
using TimeLens.Client.Models;

namespace TimeLens.Client.Data.Repositories
{
    /// <summary>
    /// 增强的LatestNewsSyncState Repository实现
    /// </summary>
    public class EnhancedLatestNewsSyncStateRepository(
        IEnhancedDuckDbContext context,
        ILogger<EnhancedLatestNewsSyncStateRepository>? logger = null)
        : EnhancedBaseRepository<LatestNewsSyncState>(context, "latest_news_sync_states", logger), ILatestNewsSyncStateRepository
    {
        protected override string GenerateCreateTableSql()
        {
            return @"
                CREATE TABLE IF NOT EXISTS latest_news_sync_states (
                    id VARCHAR PRIMARY KEY NOT NULL,
                    sync_id VARCHAR NOT NULL,
                    site INTEGER NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(site, sync_id)
                )";
        }

        protected override LatestNewsSyncState MapFromReader(DuckDBDataReader reader)
        {
            return new LatestNewsSyncState
            {
                Id = reader.GetString(0),
                SyncId = reader.GetString(1),
                Site = (SiteCategory)reader.GetInt32(2),
                CreatedAt = reader.GetDateTime(3),
                UpdatedAt = reader.GetDateTime(4)
            };
        }

        protected override DuckDBParameter[] GetInsertParameters(LatestNewsSyncState entity)
        {
            entity.CreatedAt = DateTime.UtcNow;
            entity.UpdatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.SyncId),
                new DuckDBParameter((int)entity.Site),
                new DuckDBParameter(entity.CreatedAt),
                new DuckDBParameter(entity.UpdatedAt)
            ];
        }

        protected override DuckDBParameter[] GetUpdateParameters(LatestNewsSyncState entity)
        {
            entity.UpdatedAt = DateTime.UtcNow;

            return
            [
                new DuckDBParameter(entity.Id),
                new DuckDBParameter(entity.SyncId),
                new DuckDBParameter((int)entity.Site),
                new DuckDBParameter(entity.UpdatedAt)
            ];
        }

        public override async Task<LatestNewsSyncState?> GetByIdAsync(object id)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE id = $1";
            var results = await QueryInternalAsync(sql, new DuckDBParameter(id));
            return results.FirstOrDefault();
        }

        public override async Task<IEnumerable<LatestNewsSyncState>> GetAllAsync()
        {
            const string sql = "SELECT * FROM latest_news_sync_states ORDER BY updated_at DESC";
            return await QueryInternalAsync(sql);
        }

        public override async Task<LatestNewsSyncState> AddAsync(LatestNewsSyncState entity)
        {
            const string sql = @"INSERT INTO latest_news_sync_states 
                       (id, sync_id, site, created_at, updated_at) 
                       VALUES ($1, $2, $3, $4, $5)";
            
            var parameters = GetInsertParameters(entity);
            await Context.ExecuteNonQueryAsync(sql, parameters);
            
            Logger?.LogInformation("LatestNewsSyncState {Id} for site {Site} with SyncId {SyncId} added successfully", 
                entity.Id, entity.Site, entity.SyncId);
            return entity;
        }

        public override async Task<LatestNewsSyncState> UpdateAsync(LatestNewsSyncState entity)
        {
            const string sql = @"UPDATE latest_news_sync_states
                       SET sync_id = $2, site = $3, updated_at = $4
                       WHERE id = $1";
            
            var parameters = GetUpdateParameters(entity);
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, parameters);
            
            if (rowsAffected == 0)
            {
                throw new InvalidOperationException($"LatestNewsSyncState with ID {entity.Id} not found");
            }
            
            Logger?.LogInformation("LatestNewsSyncState {Id} updated successfully", entity.Id);
            return entity;
        }

        public override async Task<bool> DeleteAsync(object id)
        {
            const string sql = "DELETE FROM latest_news_sync_states WHERE id = $1";
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(id));
            
            var deleted = rowsAffected > 0;
            if (deleted)
            {
                Logger?.LogInformation("LatestNewsSyncState {Id} deleted successfully", id);
            }
            
            return deleted;
        }

        // LatestNewsSyncState特定的查询方法
        public async Task<IEnumerable<LatestNewsSyncState>> GetBySiteAsync(SiteCategory site)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE site = $1 ORDER BY updated_at DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter((int)site));
        }

        public async Task<LatestNewsSyncState?> GetBySyncIdAsync(string syncId)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE sync_id = $1";
            var results = await QueryInternalAsync(sql, new DuckDBParameter(syncId));
            return results.FirstOrDefault();
        }

        public async Task<LatestNewsSyncState?> GetBySiteAndSyncIdAsync(SiteCategory site, string syncId)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE site = $1 AND sync_id = $2";
            var results = await QueryInternalAsync(sql, 
                new DuckDBParameter((int)site),
                new DuckDBParameter(syncId));
            return results.FirstOrDefault();
        }

        public async Task<LatestNewsSyncState?> GetLatestBySiteAsync(SiteCategory site)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE site = $1 ORDER BY updated_at DESC LIMIT 1";
            var results = await QueryInternalAsync(sql, new DuckDBParameter((int)site));
            return results.FirstOrDefault();
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetLatestForAllSitesAsync()
        {
            const string sql = @"
                SELECT DISTINCT ON (site) *
                FROM latest_news_sync_states
                ORDER BY site, updated_at DESC";
            return await QueryInternalAsync(sql);
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"SELECT * FROM latest_news_sync_states 
                       WHERE created_at >= $1 AND created_at <= $2 
                       ORDER BY created_at DESC";
            return await QueryInternalAsync(sql, 
                new DuckDBParameter(startDate),
                new DuckDBParameter(endDate));
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetByCreatedDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"SELECT * FROM latest_news_sync_states 
                       WHERE created_at >= $1 AND created_at <= $2 
                       ORDER BY created_at DESC";
            return await QueryInternalAsync(sql, 
                new DuckDBParameter(startDate),
                new DuckDBParameter(endDate));
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetByUpdatedDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"SELECT * FROM latest_news_sync_states 
                       WHERE updated_at >= $1 AND updated_at <= $2 
                       ORDER BY updated_at DESC";
            return await QueryInternalAsync(sql, 
                new DuckDBParameter(startDate),
                new DuckDBParameter(endDate));
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetCreatedAfterAsync(DateTime date)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE created_at > $1 ORDER BY created_at DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter(date));
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetUpdatedAfterAsync(DateTime date)
        {
            const string sql = "SELECT * FROM latest_news_sync_states WHERE updated_at > $1 ORDER BY updated_at DESC";
            return await QueryInternalAsync(sql, new DuckDBParameter(date));
        }

        // 统计方法
        public async Task<int> GetCountBySiteAsync(SiteCategory site)
        {
            const string sql = "SELECT COUNT(*) FROM latest_news_sync_states WHERE site = $1";
            var count = await Context.ExecuteScalarAsync<long>(sql, new DuckDBParameter((int)site));
            return (int)count;
        }

        public async Task<Dictionary<SiteCategory, int>> GetSiteStatisticsAsync()
        {
            const string sql = "SELECT site, COUNT(*) FROM latest_news_sync_states GROUP BY site";
            var results = new Dictionary<SiteCategory, int>();
            
            await using var reader = await Context.ExecuteReaderAsync(sql);
            while (await reader.ReadAsync())
            {
                var site = (SiteCategory)reader.GetInt32(0);
                var count = reader.GetInt32(1);
                results[site] = count;
            }
            
            return results;
        }

        public async Task<int> GetCountByDateAsync(DateTime date)
        {
            const string sql = @"SELECT COUNT(*) FROM latest_news_sync_states 
                       WHERE DATE(created_at) = DATE($1)";
            var count = await Context.ExecuteScalarAsync<long>(sql, new DuckDBParameter(date));
            return (int)count;
        }

        public async Task<int> GetTotalCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM latest_news_sync_states";
            var count = await Context.ExecuteScalarAsync<long>(sql);
            return (int)count;
        }

        // 高级查询
        public async Task<IEnumerable<LatestNewsSyncState>> GetRecentSyncStatesAsync(int count = 10)
        {
            var sql = $"SELECT * FROM latest_news_sync_states ORDER BY updated_at DESC LIMIT {count}";
            return await QueryInternalAsync(sql);
        }

        public async Task<IEnumerable<LatestNewsSyncState>> GetRecentBySiteAsync(SiteCategory site, int count = 10)
        {
            var sql = $"SELECT * FROM latest_news_sync_states WHERE site = $1 ORDER BY updated_at DESC LIMIT {count}";
            return await QueryInternalAsync(sql, new DuckDBParameter((int)site));
        }

        public async Task<bool> ExistsAsync(SiteCategory site, string syncId)
        {
            const string sql = "SELECT COUNT(*) FROM latest_news_sync_states WHERE site = $1 AND sync_id = $2";
            var count = await Context.ExecuteScalarAsync<long>(sql,
                new DuckDBParameter((int)site),
                new DuckDBParameter(syncId));
            return count > 0;
        }

        public async Task<LatestNewsSyncState> UpsertAsync(SiteCategory site, string syncId)
        {
            // 首先尝试查找现有记录
            var existing = await GetBySiteAndSyncIdAsync(site, syncId);

            if (existing != null)
            {
                // 更新现有记录
                existing.UpdatedAt = DateTime.UtcNow;
                return await UpdateAsync(existing);
            }
            else
            {
                // 创建新记录
                var newEntity = new LatestNewsSyncState
                {
                    SyncId = syncId,
                    Site = site
                };
                return await AddAsync(newEntity);
            }
        }

        public async Task<int> DeleteBySiteAsync(SiteCategory site)
        {
            const string sql = "DELETE FROM latest_news_sync_states WHERE site = $1";
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter((int)site));

            Logger?.LogInformation("Deleted {Count} LatestNewsSyncState records for site {Site}", rowsAffected, site);
            return rowsAffected;
        }

        public async Task<int> DeleteOlderThanAsync(DateTime date)
        {
            const string sql = "DELETE FROM latest_news_sync_states WHERE created_at < $1";
            var rowsAffected = await Context.ExecuteNonQueryAsync(sql, new DuckDBParameter(date));

            Logger?.LogInformation("Deleted {Count} LatestNewsSyncState records older than {Date}", rowsAffected, date);
            return rowsAffected;
        }

        public async Task<int> CleanupDuplicatesAsync()
        {
            // 删除重复记录，只保留每个站点和SyncId组合的最新记录
            const string sql = @"
                DELETE FROM latest_news_sync_states
                WHERE id NOT IN (
                    SELECT DISTINCT ON (site, sync_id) id
                    FROM latest_news_sync_states
                    ORDER BY site, sync_id, updated_at DESC
                )";

            var rowsAffected = await Context.ExecuteNonQueryAsync(sql);

            Logger?.LogInformation("Cleaned up {Count} duplicate LatestNewsSyncState records", rowsAffected);
            return rowsAffected;
        }
    }
}
