using System.Linq.Expressions;
using DuckDB.NET.Data;
using TimeLens.Client.Data.Context;
using TimeLens.Client.Data.Extensions;

namespace TimeLens.Client.Data.Repositories
{
    /// <summary>
    /// 增强的Repository基类，提供完整的数据库操作实现
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public abstract class EnhancedBaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly IEnhancedDuckDbContext Context;
        private readonly string _tableName;
        protected readonly ILogger? Logger;

        protected EnhancedBaseRepository(IEnhancedDuckDbContext context, string tableName, ILogger? logger = null)
        {
            Context = context;
            _tableName = tableName;
            Logger = logger;
        }

        // 抽象方法，由具体实现类提供
        public abstract Task<T?> GetByIdAsync(object id);
        public abstract Task<IEnumerable<T>> GetAllAsync();
        public abstract Task<T> AddAsync(T entity);
        public abstract Task<T> UpdateAsync(T entity);
        public abstract Task<bool> DeleteAsync(object id);
        protected abstract string GenerateCreateTableSql();
        protected abstract T MapFromReader(DuckDBDataReader reader);
        protected abstract DuckDBParameter[] GetInsertParameters(T entity);
        protected abstract DuckDBParameter[] GetUpdateParameters(T entity);

        // 通用LINQ查询实现
        public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
        {
            var (sql, parameters) = predicate.ToSql(_tableName);
            return await QueryInternalAsync(sql, parameters);
        }

        public virtual async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
        {
            var (sql, parameters) = predicate.ToSql(_tableName, limit: 1);
            var results = await QueryInternalAsync(sql, parameters);
            return results.FirstOrDefault();
        }

        public virtual async Task<bool> AnyAsync(Expression<Func<T, bool>> predicate)
        {
            var (sql, parameters) = predicate.ToSql(_tableName, selectCount: true, limit: 1);
            var count = await Context.ExecuteScalarAsync<long>(sql, parameters);
            return count > 0;
        }

        public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null)
        {
            string sql;
            DuckDBParameter[] parameters;
            
            if (predicate != null)
            {
                (sql, parameters) = predicate.ToSql(_tableName, selectCount: true);
            }
            else
            {
                sql = $"SELECT COUNT(*) FROM {_tableName}";
                parameters = [];
            }
            
            var count = await Context.ExecuteScalarAsync<long>(sql, parameters);
            return (int)count;
        }

        // 批量操作
        public virtual async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
        {
            var results = new List<T>();
            
            await Context.BeginTransactionAsync();
            try
            {
                foreach (var entity in entities)
                {
                    results.Add(await AddAsync(entity));
                }
                await Context.CommitTransactionAsync();
                return results;
            }
            catch
            {
                await Context.RollbackTransactionAsync();
                throw;
            }
        }

        public virtual async Task<bool> DeleteAsync(T entity)
        {
            // 假设实体有Id属性
            var idProperty = typeof(T).GetProperty("Id");
            if (idProperty == null)
            {
                throw new InvalidOperationException($"Entity {typeof(T).Name} must have an Id property");
            }
            
            var id = idProperty.GetValue(entity);
            return await DeleteAsync(id!);
        }

        public virtual async Task<int> DeleteRangeAsync(Expression<Func<T, bool>> predicate)
        {
            var (whereClause, parameters) = predicate.ToSqlWhere();
            var sql = $"DELETE FROM {_tableName} WHERE {whereClause}";
            return await Context.ExecuteNonQueryAsync(sql, parameters);
        }

        // 分页查询
        public virtual async Task<IEnumerable<T>> GetPagedAsync<TKey>(
            Expression<Func<T, bool>>? predicate,
            Expression<Func<T, TKey>> orderBy,
            bool ascending = true,
            int pageNumber = 1,
            int pageSize = 10)
        {
            var offset = (pageNumber - 1) * pageSize;
            var orderDirection = ascending ? "ASC" : "DESC";
            var orderColumn = orderBy.GetPropertyName();
            
            string sql;
            DuckDBParameter[] parameters;
            
            if (predicate != null)
            {
                var (whereClause, whereParams) = predicate.ToSqlWhere();
                sql = $"SELECT * FROM {_tableName} WHERE {whereClause} ORDER BY {orderColumn} {orderDirection} LIMIT {pageSize} OFFSET {offset}";
                parameters = whereParams;
            }
            else
            {
                sql = $"SELECT * FROM {_tableName} ORDER BY {orderColumn} {orderDirection} LIMIT {pageSize} OFFSET {offset}";
                parameters = [];
            }
            
            return await QueryInternalAsync(sql, parameters);
        }

        // 投影查询
        public virtual async Task<IEnumerable<TResult>> SelectAsync<TResult>(
            Expression<Func<T, bool>>? predicate,
            Expression<Func<T, TResult>> selector)
        {
            // 这是一个简化实现，实际项目中可能需要更复杂的SQL生成
            var entities = predicate != null ? await FindAsync(predicate) : await GetAllAsync();
            var compiled = selector.Compile();
            return entities.Select(compiled);
        }

        // 统计操作
        public virtual async Task<TResult> MaxAsync<TResult>(Expression<Func<T, TResult>> selector)
        {
            var column = selector.GetPropertyName();
            var sql = $"SELECT MAX({column}) FROM {_tableName}";
            return await Context.ExecuteScalarAsync<TResult>(sql) ?? default(TResult)!;
        }

        public virtual async Task<TResult> MinAsync<TResult>(Expression<Func<T, TResult>> selector)
        {
            var column = selector.GetPropertyName();
            var sql = $"SELECT MIN({column}) FROM {_tableName}";
            return await Context.ExecuteScalarAsync<TResult>(sql) ?? default(TResult)!;
        }

        public virtual async Task<decimal> SumAsync(Expression<Func<T, decimal>> selector)
        {
            var column = selector.GetPropertyName();
            var sql = $"SELECT SUM({column}) FROM {_tableName}";
            return await Context.ExecuteScalarAsync<decimal>(sql);
        }

        public virtual async Task<double> AverageAsync(Expression<Func<T, decimal>> selector)
        {
            var column = selector.GetPropertyName();
            var sql = $"SELECT AVG({column}) FROM {_tableName}";
            return await Context.ExecuteScalarAsync<double>(sql);
        }

        // 批量更新和删除
        public virtual Task<int> BulkUpdateAsync(Expression<Func<T, bool>> predicate, Expression<Func<T, T>> updateExpression)
        {
            // 简化实现，实际项目中需要更复杂的SQL生成
            throw new NotImplementedException("BulkUpdate requires custom implementation for each entity type");
        }

        public virtual async Task<int> BulkDeleteAsync(Expression<Func<T, bool>> predicate)
        {
            return await DeleteRangeAsync(predicate);
        }

        // 原始SQL查询
        public virtual async Task<IEnumerable<T>> FromSqlAsync(string sql, params object[] parameters)
        {
            var duckDbParams = parameters.Select((p, i) => new DuckDBParameter($"${i + 1}", p)).ToArray();
            return await QueryInternalAsync(sql, duckDbParams);
        }

        public virtual async Task<int> ExecuteSqlAsync(string sql, params object[] parameters)
        {
            await EnsureTableExistsAsync();
            var duckDbParams = parameters.Select((p, i) => new DuckDBParameter($"${i + 1}", p)).ToArray();
            return await Context.ExecuteNonQueryAsync(sql, duckDbParams);
        }

        // 事务支持
        public virtual async Task BeginTransactionAsync()
        {
            await Context.BeginTransactionAsync();
        }

        public virtual async Task CommitTransactionAsync()
        {
            await Context.CommitTransactionAsync();
        }

        public virtual async Task RollbackTransactionAsync()
        {
            await Context.RollbackTransactionAsync();
        }

        // 辅助方法
        protected virtual async Task EnsureTableExistsAsync()
        {
            var tableExists = await Context.TableExistsAsync(_tableName);
            if (!tableExists)
            {
                var createTableSql = GenerateCreateTableSql();
                await Context.CreateTableAsync(createTableSql);
                Logger?.LogInformation("Table {TableName} created successfully", _tableName);
            }
        }

        protected virtual async Task<IEnumerable<T>> QueryInternalAsync(string sql, params DuckDBParameter[] parameters)
        {
            await EnsureTableExistsAsync();
            return await Context.QueryAsync<T>(sql, reader => MapFromReader((DuckDBDataReader)reader), parameters);
        }
    }
}
