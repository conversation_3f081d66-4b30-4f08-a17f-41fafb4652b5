using TimeLens.Client.Data.Context;
using TimeLens.Client.Data.Repositories;
using TimeLens.Client.Models;

namespace TimeLens.Tests.Data
{
    /// <summary>
    /// ConceptTheme Repository单元测试
    /// </summary>
    public class ConceptThemeRepositoryTests : IDisposable
    {
        private readonly IEnhancedDuckDbContext _context;
        private readonly EnhancedConceptThemeRepository _repository;

        public ConceptThemeRepositoryTests()
        {
            // 使用内存数据库进行测试
            _context = new EnhancedDuckDbContext("Data Source=:memory:");
            _repository = new EnhancedConceptThemeRepository(_context);
        }

        [Fact]
        public async Task AddAsync_ShouldCreateConceptTheme_WhenValidDataProvided()
        {
            await _repository.GetThemesCreatedAfterAsync(DateTime.Today);
            // Arrange
            var conceptTheme = CreateTestConceptTheme();

            // Act
            var result = await _repository.AddAsync(conceptTheme);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(conceptTheme.Name, result.Name);
            Assert.Equal(conceptTheme.Heat, result.Heat);
            Assert.NotEqual(default(DateTime), result.CreatedAt);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnConceptTheme_WhenItemExists()
        {
            // Arrange
            var conceptTheme = CreateTestConceptTheme();
            await _repository.AddAsync(conceptTheme);

            // Act
            var result = await _repository.GetByIdAsync(conceptTheme.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(conceptTheme.Id, result.Id);
            Assert.Equal(conceptTheme.Name, result.Name);
        }

        [Fact]
        public async Task GetByHeatRangeAsync_ShouldReturnFilteredResults()
        {
            // Arrange
            var theme1 = CreateTestConceptTheme("Low Heat Theme", 20);
            var theme2 = CreateTestConceptTheme("Medium Heat Theme");
            var theme3 = CreateTestConceptTheme("High Heat Theme", 80);

            await _repository.AddAsync(theme1);
            await _repository.AddAsync(theme2);
            await _repository.AddAsync(theme3);

            // Act
            var results = await _repository.GetByHeatRangeAsync(40, 70);

            // Assert
            var conceptThemes = results as ConceptTheme[] ?? results.ToArray();
            Assert.Single(conceptThemes);
            Assert.Equal("Medium Heat Theme", conceptThemes.First().Name);
        }

        [Fact]
        public async Task GetHottestThemesAsync_ShouldReturnTopThemes()
        {
            // Arrange
            var themes = new List<ConceptTheme>
            {
                CreateTestConceptTheme("Theme 1", 30),
                CreateTestConceptTheme("Theme 2", 80),
                CreateTestConceptTheme("Theme 3", 60),
                CreateTestConceptTheme("Theme 4", 90),
                CreateTestConceptTheme("Theme 5", 40)
            };

            foreach (var theme in themes)
            {
                await _repository.AddAsync(theme);
            }

            // Act
            var results = await _repository.GetHottestThemesAsync(3);

            // Assert
            var conceptThemes = results as ConceptTheme[] ?? results.ToArray();
            Assert.Equal(3, conceptThemes.Length);
            var resultsList = conceptThemes.ToList();
            Assert.Equal(90, resultsList[0].Heat);
            Assert.Equal(80, resultsList[1].Heat);
            Assert.Equal(60, resultsList[2].Heat);
        }

        [Fact]
        public async Task GetByActiveStockAsync_ShouldReturnMatchingThemes()
        {
            // Arrange
            var theme1 = CreateTestConceptTheme("Tech Theme", 70, ["AAPL", "MSFT", "GOOGL"]);
            var theme2 = CreateTestConceptTheme("Finance Theme", 60, ["JPM", "BAC", "WFC"]);
            var theme3 = CreateTestConceptTheme("Mixed Theme", 50, ["AAPL", "JPM", "TSLA"]);

            await _repository.AddAsync(theme1);
            await _repository.AddAsync(theme2);
            await _repository.AddAsync(theme3);

            // Act
            var results = await _repository.GetByActiveStockAsync("AAPL");

            // Assert
            var conceptThemes = results as ConceptTheme[] ?? results.ToArray();
            Assert.Equal(2, conceptThemes.Length);
            Assert.All(conceptThemes, theme => Assert.Contains("AAPL", theme.ActiveStocks));
        }

        [Fact]
        public async Task SearchByNameAsync_ShouldReturnMatchingResults()
        {
            // Arrange
            var theme1 = CreateTestConceptTheme("Technology Innovation");
            var theme2 = CreateTestConceptTheme("Green Energy");
            var theme3 = CreateTestConceptTheme("Tech Startups");

            await _repository.AddAsync(theme1);
            await _repository.AddAsync(theme2);
            await _repository.AddAsync(theme3);

            // Act
            var results = await _repository.SearchByNameAsync("Tech");

            // Assert
            var conceptThemes = results as ConceptTheme[] ?? results.ToArray();
            Assert.Equal(2, conceptThemes.Length);
            Assert.All(conceptThemes, theme => Assert.Contains("Tech", theme.Name));
        }

        [Fact]
        public async Task GetAverageHeatAsync_ShouldReturnCorrectAverage()
        {
            // Arrange
            var themes = new List<ConceptTheme>
            {
                CreateTestConceptTheme("Theme 1", 20),
                CreateTestConceptTheme("Theme 2", 40),
                CreateTestConceptTheme("Theme 3", 60),
                CreateTestConceptTheme("Theme 4", 80)
            };

            foreach (var theme in themes)
            {
                await _repository.AddAsync(theme);
            }

            // Act
            var averageHeat = await _repository.GetAverageHeatAsync();

            // Assert
            Assert.Equal(50.0, averageHeat);
        }

        [Fact]
        public async Task UpdateAsync_ShouldModifyConceptTheme()
        {
            // Arrange
            var conceptTheme = CreateTestConceptTheme();
            await _repository.AddAsync(conceptTheme);

            conceptTheme.Name = "Updated Theme Name";
            conceptTheme.Heat = 95;

            // Act
            var result = await _repository.UpdateAsync(conceptTheme);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Updated Theme Name", result.Name);
            Assert.Equal(95, result.Heat);
            Assert.True(result.UpdatedAt > result.CreatedAt);
        }

        [Fact]
        public async Task DeleteAsync_ShouldRemoveConceptTheme()
        {
            // Arrange
            var conceptTheme = CreateTestConceptTheme();
            await _repository.AddAsync(conceptTheme);

            // Act
            var result = await _repository.DeleteAsync(conceptTheme.Id);

            // Assert
            Assert.True(result);

            // Verify item is deleted
            var deletedItem = await _repository.GetByIdAsync(conceptTheme.Id);
            Assert.Null(deletedItem);
        }

        [Fact]
        public async Task GetHeatDistributionAsync_ShouldReturnCorrectDistribution()
        {
            // Arrange
            var themes = new List<ConceptTheme>
            {
                CreateTestConceptTheme("Low 1", 10),
                CreateTestConceptTheme("Low 2", 15),
                CreateTestConceptTheme("Medium 1", 30),
                CreateTestConceptTheme("Medium 2", 35),
                CreateTestConceptTheme("High 1", 70),
                CreateTestConceptTheme("Very High 1", 90)
            };

            foreach (var theme in themes)
            {
                await _repository.AddAsync(theme);
            }

            // Act
            var distribution = await _repository.GetHeatDistributionAsync();

            // Assert
            Assert.True(distribution.ContainsKey(0)); // 0-19 range
            Assert.True(distribution.ContainsKey(20)); // 20-39 range
            Assert.True(distribution.ContainsKey(60)); // 60-79 range
            Assert.True(distribution.ContainsKey(80)); // 80+ range
        }

        [Fact]
        public async Task CountAsync_ShouldReturnCorrectCount()
        {
            // Arrange
            await _repository.AddAsync(CreateTestConceptTheme("Theme 1", 30));
            await _repository.AddAsync(CreateTestConceptTheme("Theme 2", 70));
            await _repository.AddAsync(CreateTestConceptTheme("Theme 3"));

            // Act
            var totalCount = await _repository.CountAsync();
            var highHeatCount = await _repository.CountAsync(x => x.Heat > 60);

            // Assert
            Assert.Equal(3, totalCount);
            Assert.Equal(1, highHeatCount);
        }

        private ConceptTheme CreateTestConceptTheme(string name = "Test Theme", int heat = 50, string[]? activeStocks = null)
        {
            return new ConceptTheme
            {
                Name = name,
                Heat = heat,
                ActiveStocks = activeStocks?.ToList() ?? ["TEST001", "TEST002"]
            };
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
